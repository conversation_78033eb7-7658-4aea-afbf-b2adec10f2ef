# Simple Volume Management Tool - No Admin Check
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create form
$form = New-Object System.Windows.Forms.Form
$form.Text = "Volume Management - Simple"
$form.Size = New-Object System.Drawing.Size(800, 600)
$form.StartPosition = "CenterScreen"
$form.BackColor = [System.Drawing.Color]::Black

# Title
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "VOLUME MANAGEMENT"
$titleLabel.Location = New-Object System.Drawing.Point(0, 10)
$titleLabel.Size = New-Object System.Drawing.Size(800, 40)
$titleLabel.ForeColor = [System.Drawing.Color]::Lime
$titleLabel.Font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
$form.Controls.Add($titleLabel)

# Drive list
$driveListBox = New-Object System.Windows.Forms.ListBox
$driveListBox.Location = New-Object System.Drawing.Point(20, 60)
$driveListBox.Size = New-Object System.Drawing.Size(750, 100)
$driveListBox.BackColor = [System.Drawing.Color]::Black
$driveListBox.ForeColor = [System.Drawing.Color]::Lime
$driveListBox.Font = New-Object System.Drawing.Font("Consolas", 10)
$form.Controls.Add($driveListBox)

# Status box
$statusTextBox = New-Object System.Windows.Forms.TextBox
$statusTextBox.Multiline = $true
$statusTextBox.ScrollBars = "Vertical"
$statusTextBox.Location = New-Object System.Drawing.Point(20, 400)
$statusTextBox.Size = New-Object System.Drawing.Size(750, 150)
$statusTextBox.BackColor = [System.Drawing.Color]::Black
$statusTextBox.ForeColor = [System.Drawing.Color]::Lime
$statusTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$statusTextBox.ReadOnly = $true
$statusTextBox.Text = "Ready..."
$form.Controls.Add($statusTextBox)

# Function to add status
function Add-Status {
    param([string]$message)
    $timestamp = Get-Date -Format "HH:mm:ss"
    $statusTextBox.AppendText("[$timestamp] $message`r`n")
    $statusTextBox.ScrollToCaret()
    [System.Windows.Forms.Application]::DoEvents()
}

# Function to update drive list
function Update-DriveList {
    $driveListBox.Items.Clear()
    try {
        $drives = Get-WmiObject Win32_LogicalDisk | Select-Object @{Name = 'Name'; Expression = { $_.DeviceID } },
        @{Name = 'VolumeName'; Expression = { $_.VolumeName } },
        @{Name = 'Size (GB)'; Expression = { [math]::round($_.Size / 1GB, 0) } },
        @{Name = 'FreeSpace (GB)'; Expression = { [math]::round($_.FreeSpace / 1GB, 0) } }

        foreach ($drive in $drives) {
            $driveInfo = "$($drive.Name) - $($drive.VolumeName) - Size: $($drive.'Size (GB)') GB - Free: $($drive.'FreeSpace (GB)') GB"
            $driveListBox.Items.Add($driveInfo)
        }

        if ($driveListBox.Items.Count -gt 0) {
            $driveListBox.SelectedIndex = 0
        }
        return $drives.Count
    }
    catch {
        Add-Status "Error getting drive list: $_"
        return 0
    }
}

# Test button
$testButton = New-Object System.Windows.Forms.Button
$testButton.Text = "Test Shrink Volume"
$testButton.Location = New-Object System.Drawing.Point(20, 180)
$testButton.Size = New-Object System.Drawing.Size(200, 50)
$testButton.BackColor = [System.Drawing.Color]::Green
$testButton.ForeColor = [System.Drawing.Color]::White
$testButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$testButton.Add_Click({
    Add-Status "Test button clicked!"
    Add-Status "This would normally run the shrink volume function."
    Add-Status "File is working correctly!"
})
$form.Controls.Add($testButton)

# Refresh button
$refreshButton = New-Object System.Windows.Forms.Button
$refreshButton.Text = "Refresh Drives"
$refreshButton.Location = New-Object System.Drawing.Point(240, 180)
$refreshButton.Size = New-Object System.Drawing.Size(150, 50)
$refreshButton.BackColor = [System.Drawing.Color]::Blue
$refreshButton.ForeColor = [System.Drawing.Color]::White
$refreshButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$refreshButton.Add_Click({
    Add-Status "Refreshing drive list..."
    $count = Update-DriveList
    Add-Status "Found $count drives."
})
$form.Controls.Add($refreshButton)

# Exit button
$exitButton = New-Object System.Windows.Forms.Button
$exitButton.Text = "Exit"
$exitButton.Location = New-Object System.Drawing.Point(410, 180)
$exitButton.Size = New-Object System.Drawing.Size(100, 50)
$exitButton.BackColor = [System.Drawing.Color]::Red
$exitButton.ForeColor = [System.Drawing.Color]::White
$exitButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$exitButton.Add_Click({
    $form.Close()
})
$form.Controls.Add($exitButton)

# Load drives on startup
Add-Status "Loading drives..."
$count = Update-DriveList
Add-Status "Found $count drives."

# Show form
$form.ShowDialog()
