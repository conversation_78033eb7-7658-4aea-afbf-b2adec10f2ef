# Simple test to check if PowerShell GUI works
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create form
$form = New-Object System.Windows.Forms.Form
$form.Text = "Test Form"
$form.Size = New-Object System.Drawing.Size(400, 300)
$form.StartPosition = "CenterScreen"
$form.BackColor = [System.Drawing.Color]::Black

# Create button
$button = New-Object System.Windows.Forms.Button
$button.Text = "Test Button"
$button.Location = New-Object System.Drawing.Point(150, 100)
$button.Size = New-Object System.Drawing.Size(100, 50)
$button.BackColor = [System.Drawing.Color]::Green
$button.ForeColor = [System.Drawing.Color]::White
$button.Add_Click({
    [System.Windows.Forms.MessageBox]::Show("Button clicked!")
})

$form.Controls.Add($button)

# Show form
Write-Host "Showing form..."
$form.ShowDialog()
Write-Host "Form closed."
